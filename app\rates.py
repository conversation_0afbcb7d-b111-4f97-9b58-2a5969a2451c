import os
import uuid
import pandas as pd
import tempfile
import numpy as np
from flask import Blueprint, request, jsonify, session, render_template, redirect, url_for, current_app, send_file
from app.config import get_db_connection
from app.services.audit_log import AuditLogService
from datetime import datetime, timedelta
import xlsxwriter
import pymysql
import json
from decimal import Decimal

rates_bp = Blueprint('rates', __name__)

@rates_bp.route('/get_books_with_rates', methods=['GET'])
def get_books_with_rates():
    """获取带费率信息的样书列表"""
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})

    # 检查用户是否有查看费率信息的权限
    from app.users.publisher import check_user_permission
    if not check_user_permission(session['user_id'], 'view_rate_info'):
        return jsonify({"code": 1, "message": "您没有查看费率信息的权限"})
    
    publisher_id = session['user_id']
    try:
        page = int(request.args.get('page', 1))
        limit_param = request.args.get('limit', '15')

        # 处理特殊的limit值
        if limit_param == 'all' or int(limit_param) > 10000:
            limit = 999999  # 设置一个很大的数字表示全部
            offset = 0
        else:
            limit = int(limit_param)
            offset = (page - 1) * limit

        search = request.args.get('search', '')
        price_status = request.args.get('price_status')
        rate_status = request.args.get('rate_status')

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 构建基本查询条件
            where_clause = "sb.publisher_id = %s"
            params = [publisher_id]

            # 搜索条件
            if search and search.strip():
                where_clause += " AND (sb.name LIKE %s OR sb.author LIKE %s OR sb.isbn LIKE %s)"
                params.extend([f'%{search}%', f'%{search}%', f'%{search}%'])

            # 价格状态筛选（单选）
            if price_status:
                if price_status == 'has_price':
                    where_clause += " AND sb.price IS NOT NULL AND sb.price > 0"
                elif price_status == 'no_price':
                    where_clause += " AND (sb.price IS NULL OR sb.price = 0)"

            # 费率状态筛选（AND逻辑，同时满足所有选中条件）
            if rate_status:
                import json
                rate_filters = json.loads(rate_status)
                for rf in rate_filters:
                    if rf == 'has_shipping':
                        where_clause += " AND sb.shipping_discount IS NOT NULL"
                    elif rf == 'has_settlement':
                        where_clause += " AND sb.settlement_discount IS NOT NULL"
                    elif rf == 'has_promotion':
                        where_clause += " AND sb.promotion_rate IS NOT NULL"
                    elif rf == 'auto_promotion':
                        where_clause += " AND sb.promotion_rate IS NULL"

            # 查询样书及费率信息
            if limit >= 999999:  # 全部数据
                sql = f"""
                    SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price,
                           sb.shipping_discount, sb.settlement_discount, sb.promotion_rate,
                           sb.publisher_name, sb.level, sb.book_type,
                           sb.publication_date, d.name as directory_name
                    FROM sample_books sb
                    LEFT JOIN directories d ON sb.parent_id = d.id
                    WHERE {where_clause}
                    ORDER BY sb.id DESC
                """
            else:
                sql = f"""
                    SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price,
                           sb.shipping_discount, sb.settlement_discount, sb.promotion_rate,
                           sb.publisher_name, sb.level, sb.book_type,
                           sb.publication_date, d.name as directory_name
                    FROM sample_books sb
                    LEFT JOIN directories d ON sb.parent_id = d.id
                    WHERE {where_clause}
                    ORDER BY sb.id DESC
                    LIMIT %s OFFSET %s
                """
                params.extend([limit, offset])

            cursor.execute(sql, params)
            books = cursor.fetchall()
            
            # 格式化数据
            for book in books:
                # 格式化日期
                if book.get('publication_date'):
                    book['publication_date'] = book['publication_date'].strftime('%Y-%m-%d')

                # 判断推广费率来源和自动计算开关状态
                if book.get('promotion_rate') is not None:
                    # 有推广费率值，表示用户手动设置
                    book['promotion_rate_source'] = 'manual'
                    book['auto_calculate_promotion'] = False
                    book['promotion_rate_display'] = f"{float(book['promotion_rate']) * 100:.1f}%"
                else:
                    # 无推广费率值，使用自动计算
                    book['promotion_rate_source'] = 'auto'
                    book['auto_calculate_promotion'] = True
                    # 自动计算推广费率 = 发货折扣 - 结算折扣
                    if book.get('shipping_discount') and book.get('settlement_discount'):
                        calculated_rate = float(book['shipping_discount']) - float(book['settlement_discount'])
                        book['calculated_promotion_rate'] = calculated_rate
                        book['promotion_rate_display'] = f"{calculated_rate * 100:.1f}% (自动)"
                    else:
                        book['calculated_promotion_rate'] = 0
                        book['promotion_rate_display'] = "无法计算"
            
            # 查询总数
            count_sql = f"""
                SELECT COUNT(*) as total
                FROM sample_books sb
                WHERE {where_clause}
            """
            # 根据是否有limit和offset参数来决定如何传递参数
            if limit >= 999999:
                count_params = params
            else:
                count_params = params[:-2]  # 去掉limit和offset参数

            cursor.execute(count_sql, count_params)
            result = cursor.fetchone()
            total = result['total']
            
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": {
                    "books": books,
                    "total": total,
                    "page": page,
                    "limit": limit
                }
            })

    except Exception as e:
        print(f"获取费率数据失败: {str(e)}")  # 添加调试信息
        return jsonify({"code": 1, "message": f"获取数据失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@rates_bp.route('/update_book_rates', methods=['POST'])
def update_book_rates():
    """更新单本书的费率信息"""
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    book_id = request.form.get('book_id')
    price = request.form.get('price')
    shipping_discount = request.form.get('shipping_discount')
    settlement_discount = request.form.get('settlement_discount')
    promotion_rate = request.form.get('promotion_rate')
    
    if not book_id:
        return jsonify({"code": 1, "message": "书籍ID不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证书籍是否属于该出版社
            cursor.execute("""
                SELECT id FROM sample_books 
                WHERE id = %s AND publisher_id = %s
            """, (book_id, publisher_id))
            
            if not cursor.fetchone():
                return jsonify({"code": 1, "message": "书籍不存在或无权限修改"})
            
            # 构建更新SQL
            update_fields = []
            update_params = []
            
            if price and price.strip():
                try:
                    price_value = float(price)
                    if price_value <= 0:
                        return jsonify({"code": 1, "message": "价格必须大于0"})
                    update_fields.append("price = %s")
                    update_params.append(price_value)
                except:
                    return jsonify({"code": 1, "message": "价格格式不正确"})
            
            if shipping_discount and shipping_discount.strip():
                try:
                    shipping_value = float(shipping_discount) / 100  # 转换为小数
                    if shipping_value <= 0 or shipping_value > 1:
                        return jsonify({"code": 1, "message": "发货折扣必须在0-100之间"})
                    update_fields.append("shipping_discount = %s")
                    update_params.append(shipping_value)
                except:
                    return jsonify({"code": 1, "message": "发货折扣格式不正确"})
            
            if settlement_discount and settlement_discount.strip():
                try:
                    settlement_value = float(settlement_discount) / 100  # 转换为小数
                    if settlement_value <= 0 or settlement_value > 1:
                        return jsonify({"code": 1, "message": "结算折扣必须在0-100之间"})
                    update_fields.append("settlement_discount = %s")
                    update_params.append(settlement_value)
                except:
                    return jsonify({"code": 1, "message": "结算折扣格式不正确"})
            
            if promotion_rate and promotion_rate.strip():
                try:
                    promotion_value = float(promotion_rate) / 100  # 转换为小数
                    if promotion_value < 0 or promotion_value > 1:
                        return jsonify({"code": 1, "message": "推广费率必须在0-100之间"})
                    update_fields.append("promotion_rate = %s")
                    update_params.append(promotion_value)
                except:
                    return jsonify({"code": 1, "message": "推广费率格式不正确"})
            
            if not update_fields:
                return jsonify({"code": 1, "message": "没有需要更新的字段"})
            
            # 执行更新
            update_sql = f"""
                UPDATE sample_books 
                SET {', '.join(update_fields)}
                WHERE id = %s AND publisher_id = %s
            """
            update_params.extend([book_id, publisher_id])
            
            cursor.execute(update_sql, update_params)
            connection.commit()
            
            return jsonify({
                "code": 0,
                "message": "更新成功"
            })
            
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"更新失败: {str(e)}"})
    finally:
        connection.close()

@rates_bp.route('/toggle_auto_calculate', methods=['POST'])
def toggle_auto_calculate():
    """切换推广费率自动计算模式"""
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})

    publisher_id = session['user_id']
    book_id = request.form.get('book_id')
    auto_calculate = request.form.get('auto_calculate') == 'true'

    if not book_id:
        return jsonify({"code": 1, "message": "书籍ID不能为空"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证书籍是否属于该出版社
            cursor.execute("""
                SELECT id, shipping_discount, settlement_discount, promotion_rate
                FROM sample_books
                WHERE id = %s AND publisher_id = %s
            """, (book_id, publisher_id))

            book = cursor.fetchone()
            if not book:
                return jsonify({"code": 1, "message": "书籍不存在或无权限修改"})

            if auto_calculate:
                # 开启自动计算：清除手动设置的推广费率
                cursor.execute("""
                    UPDATE sample_books
                    SET promotion_rate = NULL
                    WHERE id = %s AND publisher_id = %s
                """, (book_id, publisher_id))

                # 计算自动费率
                calculated_rate = 0
                if book['shipping_discount'] and book['settlement_discount']:
                    calculated_rate = float(book['shipping_discount']) - float(book['settlement_discount'])

                message = f"已开启自动计算，推广费率为 {calculated_rate * 100:.1f}%"
            else:
                # 关闭自动计算：使用当前计算值作为手动值
                calculated_rate = 0
                if book['shipping_discount'] and book['settlement_discount']:
                    calculated_rate = float(book['shipping_discount']) - float(book['settlement_discount'])

                cursor.execute("""
                    UPDATE sample_books
                    SET promotion_rate = %s
                    WHERE id = %s AND publisher_id = %s
                """, (calculated_rate, book_id, publisher_id))

                message = f"已关闭自动计算，当前推广费率为 {calculated_rate * 100:.1f}%"

            connection.commit()

            return jsonify({
                "code": 0,
                "message": message,
                "data": {
                    "auto_calculate": auto_calculate,
                    "calculated_rate": calculated_rate
                }
            })

    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"切换失败: {str(e)}"})
    finally:
        connection.close()

@rates_bp.route('/batch_update_rates', methods=['POST'])
def batch_update_rates():
    """批量更新费率信息"""
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})

    publisher_id = session['user_id']
    book_ids = request.form.getlist('book_ids[]')
    update_type = request.form.get('update_type')  # price, shipping_discount, settlement_discount, promotion_rate
    update_value = request.form.get('update_value')

    if not book_ids or not update_type or not update_value:
        return jsonify({"code": 1, "message": "参数不完整"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证所有书籍都属于该出版社
            placeholders = ','.join(['%s'] * len(book_ids))
            cursor.execute(f"""
                SELECT COUNT(*) as count FROM sample_books
                WHERE id IN ({placeholders}) AND publisher_id = %s
            """, book_ids + [publisher_id])

            result = cursor.fetchone()
            if result['count'] != len(book_ids):
                return jsonify({"code": 1, "message": "部分书籍不存在或无权限修改"})

            # 验证更新值
            if update_type == 'price':
                try:
                    value = float(update_value)
                    if value <= 0:
                        return jsonify({"code": 1, "message": "价格必须大于0"})
                except:
                    return jsonify({"code": 1, "message": "价格格式不正确"})
            else:
                try:
                    value = float(update_value) / 100  # 转换为小数
                    if value < 0 or value > 1:
                        return jsonify({"code": 1, "message": "费率必须在0-100之间"})
                except:
                    return jsonify({"code": 1, "message": "费率格式不正确"})

            # 执行批量更新
            update_sql = f"""
                UPDATE sample_books
                SET {update_type} = %s
                WHERE id IN ({placeholders}) AND publisher_id = %s
            """
            cursor.execute(update_sql, [value] + book_ids + [publisher_id])
            connection.commit()

            return jsonify({
                "code": 0,
                "message": f"成功更新{len(book_ids)}本书的{update_type}"
            })

    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"批量更新失败: {str(e)}"})
    finally:
        connection.close()

@rates_bp.route('/get_directories', methods=['GET'])
def get_directories():
    """获取目录列表"""
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})

    publisher_id = session['user_id']

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询该出版社的所有目录
            sql = """
                SELECT d.id, d.name, d.parent_id, COUNT(sb.id) as book_count
                FROM directories d
                LEFT JOIN sample_books sb ON d.id = sb.parent_id
                WHERE d.publisher_id = %s
                GROUP BY d.id
                ORDER BY d.name
            """
            cursor.execute(sql, (publisher_id,))
            directories = cursor.fetchall()

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": directories
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取目录失败: {str(e)}"})
    finally:
        connection.close()

@rates_bp.route('/export_rates', methods=['GET'])
def export_rates():
    """导出费率数据"""
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})

    publisher_id = session['user_id']
    search = request.args.get('search', '')
    price_status = request.args.get('price_status')
    rate_status = request.args.get('rate_status')
    directories = request.args.get('directories')

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取出版社信息
            cursor.execute("""
                SELECT pc.name as company_name
                FROM users u
                JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                WHERE u.user_id = %s
            """, (publisher_id,))
            publisher_info = cursor.fetchone()
            company_name = publisher_info['company_name'] if publisher_info else '未知出版社'

            # 构建查询条件（与get_books_with_rates相同的逻辑）
            where_clause = "sb.publisher_id = %s"
            params = [publisher_id]

            # 搜索条件
            if search:
                where_clause += " AND (sb.name LIKE %s OR sb.author LIKE %s OR sb.isbn LIKE %s)"
                params.extend([f'%{search}%', f'%{search}%', f'%{search}%'])

            # 价格状态筛选（单选）
            if price_status:
                if price_status == 'has_price':
                    where_clause += " AND sb.price IS NOT NULL AND sb.price > 0"
                elif price_status == 'no_price':
                    where_clause += " AND (sb.price IS NULL OR sb.price = 0)"

            # 费率状态筛选（AND逻辑，同时满足所有选中条件）
            if rate_status:
                import json
                rate_filters = json.loads(rate_status)
                for rf in rate_filters:
                    if rf == 'has_shipping':
                        where_clause += " AND sb.shipping_discount IS NOT NULL"
                    elif rf == 'has_settlement':
                        where_clause += " AND sb.settlement_discount IS NOT NULL"
                    elif rf == 'has_promotion':
                        where_clause += " AND sb.promotion_rate IS NOT NULL"
                    elif rf == 'auto_promotion':
                        where_clause += " AND sb.promotion_rate IS NULL"

            # 目录筛选
            if directories:
                import json
                dir_list = json.loads(directories)
                if dir_list:
                    placeholders = ','.join(['%s'] * len(dir_list))
                    where_clause += f" AND sb.parent_id IN ({placeholders})"
                    params.extend(dir_list)

            # 查询数据
            sql = f"""
                SELECT sb.name, sb.author, sb.isbn, sb.price,
                       sb.shipping_discount, sb.settlement_discount, sb.promotion_rate,
                       sb.publisher_name, sb.level, sb.book_type,
                       sb.publication_date, d.name as directory_name
                FROM sample_books sb
                LEFT JOIN directories d ON sb.parent_id = d.id
                WHERE {where_clause}
                ORDER BY sb.name
            """

            cursor.execute(sql, params)
            books = cursor.fetchall()

            if not books:
                return jsonify({"code": 1, "message": "暂无数据可导出"})

            # 处理数据
            data = []
            for book in books:
                data.append({
                    '书名': book['name'] or '',
                    '作者': book['author'] or '',
                    'ISBN': book['isbn'] or '',
                    '定价': f"¥{book['price']}" if book['price'] else '',
                    '发货折扣': f"{float(book['shipping_discount']) * 100:.1f}%" if book['shipping_discount'] else '',
                    '结算折扣': f"{float(book['settlement_discount']) * 100:.1f}%" if book['settlement_discount'] else '',
                    '推广费率': f"{float(book['promotion_rate']) * 100:.1f}%" if book['promotion_rate'] else '',
                    '版别': book['publisher_name'] or '',
                    '层次': book['level'] or '',
                    '类型': book['book_type'] or '',
                    '出版时间': book['publication_date'].strftime('%Y-%m-%d') if book['publication_date'] else '',
                    '目录': book['directory_name'] or '根目录'
                })

            # 创建Excel文件
            df = pd.DataFrame(data)
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')

            with pd.ExcelWriter(temp_file.name, engine='xlsxwriter') as writer:
                df.to_excel(writer, sheet_name='费率价格数据', index=False)

                # 设置列宽
                workbook = writer.book
                worksheet = writer.sheets['费率价格数据']

                column_widths = {
                    '书名': 30, '作者': 20, 'ISBN': 15, '定价': 10,
                    '发货折扣': 12, '结算折扣': 12, '推广费率': 12,
                    '版别': 20, '层次': 10, '类型': 10, '出版时间': 12, '目录': 20
                }

                for i, col in enumerate(df.columns):
                    if col in column_widths:
                        worksheet.set_column(i, i, column_widths[col])

                # 添加表头格式
                header_format = workbook.add_format({
                    'bold': True,
                    'text_wrap': True,
                    'valign': 'top',
                    'fg_color': '#D7E4BC',
                    'border': 1
                })

                for col_num, value in enumerate(df.columns.values):
                    worksheet.write(0, col_num, value, header_format)

            # 生成文件名
            current_time = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{company_name}_费率价格数据_{current_time}.xlsx"

            return send_file(
                temp_file.name,
                as_attachment=True,
                download_name=filename,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

    except Exception as e:
        return jsonify({"code": 1, "message": f"导出失败: {str(e)}"})
    finally:
        connection.close()
